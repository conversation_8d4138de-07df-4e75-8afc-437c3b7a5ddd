import flask
import requests
import os
import json
from flask import request
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin

app = flask.Flask(__name__)
googlebot_headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.119 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)"
}

# FlareSolverr configuration
FLARESOLVERR_HOST = os.getenv('FLARESOLVERR_HOST', None)

def is_cloudflare_blocked(response_text):
    """
    Check if the response indicates a Cloudflare block
    """
    cloudflare_indicators = [
        "Attention Required! | Cloudflare",
        "Sorry, you have been blocked",
        "Ray ID:",
        "cloudflare",
        "cf-browser-verification",
        "cf-challenge-form",
        "DDoS protection by Cloudflare"
    ]

    response_lower = response_text.lower()
    return any(indicator.lower() in response_lower for indicator in cloudflare_indicators)

def solve_with_flaresolverr(url):
    """
    Use FlareSolverr to bypass Cloudflare protection
    """
    if not FLARESOLVERR_HOST:
        raise Exception("FlareSolverr is not configured. Set FLARESOLVERR_HOST environment variable.")

    flaresolverr_url = f"{FLARESOLVERR_HOST}/v1"

    payload = {
        "cmd": "request.get",
        "url": url,
        "maxTimeout": 60000
    }

    try:
        response = requests.post(
            flaresolverr_url,
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=70  # Slightly longer than maxTimeout
        )
        response.raise_for_status()

        result = response.json()

        if result.get("status") == "ok" and "solution" in result:
            solution = result["solution"]
            return solution["response"], solution["url"]
        else:
            raise Exception(f"FlareSolverr failed: {result.get('message', 'Unknown error')}")

    except requests.exceptions.RequestException as e:
        raise Exception(f"Failed to connect to FlareSolverr: {str(e)}")
    except json.JSONDecodeError as e:
        raise Exception(f"Invalid response from FlareSolverr: {str(e)}")
html = """
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>13ft Ladder</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans&display=swap" rel="stylesheet" async>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #FFF;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 90vh;
            transition: background-color 0.3s, color 0.3s;
        }

        h1 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }

        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
        }

        input[type=text] {
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            width: 100%;
            font-size: 1rem;
            box-sizing: border-box;
        }

        input[type="submit"] {
            padding: 10px;
            background-color: #6a0dad;
            color: #fff;
            border: none;
            border-radius: 5px;
            width: 100%;
            text-transform: uppercase;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        input[type="submit"]:hover {
            background-color: #4e0875;
        }

        /* Toggle switch styles */
        .dark-mode-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .dark-mode-toggle input {
            display: none;
        }

        .dark-mode-toggle label {
            cursor: pointer;
            text-indent: -9999px;
            width: 52px;
            height: 27px;
            background: grey;
            display: block;
            border-radius: 100px;
            position: relative;
        }

        .dark-mode-toggle label:after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 23px;
            height: 23px;
            background: #fff;
            border-radius: 90px;
            transition: 0.3s;
        }

        .dark-mode-toggle input:checked+label {
            background: #6a0dad;
        }

        .dark-mode-toggle input:checked+label:after {
            left: calc(100% - 2px);
            transform: translateX(-100%);
        }

        /* Responsive adjustments */
        @media only screen and (max-width: 600px) {
            form {
                padding: 10px;
            }

            h1 {
                font-size: 1.2rem;
            }
        }

        /* Dark mode styles */
        body.dark-mode {
            background-color: #333;
            color: #FFF;
        }

        body.dark-mode h1 {
            color: #FFF;
        }

        body.dark-mode input[type=text] {
            background-color: #555;
            border: 1px solid #777;
            color: #FFF;
        }

        body.dark-mode input[type="submit"] {
            background-color: #9b30ff;
        }

        body.dark-mode input[type="submit"]:hover {
            background-color: #7a1bb5;
        }
    </style>
</head>

<body>
    <div class="dark-mode-toggle">
        <input type="checkbox" id="dark-mode-toggle">
        <label for="dark-mode-toggle" title="Toggle Dark Mode"></label>
    </div>
    <form action="/article" method="post">
        <h1>Enter Website Link</h1>
        <label for="link">Link of the website you want to remove paywall for:</label>
        <input type="text" id="link" name="link" required autofocus>
        <input type="submit" value="Submit">
    </form>

    <script>
        const toggleSwitch = document.getElementById('dark-mode-toggle');
        const currentTheme = localStorage.getItem('theme') || (window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light");

        if (currentTheme === "dark") {
            document.body.classList.add("dark-mode");
            toggleSwitch.checked = true;
        }

        toggleSwitch.addEventListener('change', function () {
            if (this.checked) {
                document.body.classList.add("dark-mode");
                localStorage.setItem('theme', 'dark');
            } else {
                document.body.classList.remove("dark-mode");
                localStorage.setItem('theme', 'light');
            }
        });
    </script>
</body>

</html>
"""

def add_base_tag(html_content, original_url):
    soup = BeautifulSoup(html_content, 'html.parser')
    parsed_url = urlparse(original_url)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}/"
    
    # Handle paths that are not root, e.g., "https://x.com/some/path/w.html"
    if parsed_url.path and not parsed_url.path.endswith('/'):
        base_url = urljoin(base_url, parsed_url.path.rsplit('/', 1)[0] + '/')
    base_tag = soup.find('base')
    
    print(base_url)
    if not base_tag:
        new_base_tag = soup.new_tag('base', href=base_url)
        if soup.head:
            soup.head.insert(0, new_base_tag)
        else:
            head_tag = soup.new_tag('head')
            head_tag.insert(0, new_base_tag)
            soup.insert(0, head_tag)
    
    return str(soup)

def bypass_paywall(url):
    """
    Bypass paywall for a given url
    """
    if url.startswith("http"):
        # First try with regular GoogleBot headers
        try:
            print(f"Making initial request to: {url}", flush=True)
            response = requests.get(url, headers=googlebot_headers, timeout=30)
            response.encoding = response.apparent_encoding
            print(f"Initial response status: {response.status_code}", flush=True)

            # Check if we're blocked by Cloudflare
            if is_cloudflare_blocked(response.text):
                print(f"Cloudflare detected for {url}, attempting FlareSolverr bypass...", flush=True)

                if FLARESOLVERR_HOST:
                    try:
                        flare_content, flare_url = solve_with_flaresolverr(url)
                        print(f"FlareSolverr successful for {url}", flush=True)
                        return add_base_tag(flare_content, flare_url)
                    except Exception as flare_error:
                        print(f"FlareSolverr failed: {flare_error}", flush=True)
                        # Fall back to original response even if blocked
                        return add_base_tag(response.text, response.url)
                else:
                    print("FlareSolverr not configured, returning blocked content", flush=True)
                    return add_base_tag(response.text, response.url)

            # No Cloudflare block detected, return normal response
            print(f"No Cloudflare block detected for {url}", flush=True)
            return add_base_tag(response.text, response.url)

        except requests.exceptions.RequestException as e:
            print(f"Request failed for {url}: {e}", flush=True)
            raise e

    try:
        return bypass_paywall("https://" + url)
    except requests.exceptions.RequestException:
        return bypass_paywall("http://" + url)


@app.route("/")
def main_page():
    return html


@app.route("/article", methods=["POST"])
def show_article():
    link = flask.request.form["link"]
    try:
        print(f"Processing request for: {link}", flush=True)
        result = bypass_paywall(link)
        print(f"Successfully processed: {link}", flush=True)

        # Create a proper Flask response with correct content type
        response = flask.make_response(result)
        response.headers['Content-Type'] = 'text/html; charset=utf-8'
        return response

    except requests.exceptions.RequestException as e:
        print(f"Request exception for {link}: {e}", flush=True)
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head><title>Error</title></head>
        <body>
            <h1>Error accessing website</h1>
            <p>Failed to access: {link}</p>
            <p>Error: {str(e)}</p>
            <a href="/">Try another URL</a>
        </body>
        </html>
        """
        return error_html, 400

    except Exception as e:
        print(f"Unexpected error for {link}: {e}", flush=True)
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head><title>Unexpected Error</title></head>
        <body>
            <h1>Unexpected Error</h1>
            <p>URL: {link}</p>
            <p>Error: {str(e)}</p>
            <a href="/">Try another URL</a>
        </body>
        </html>
        """
        return error_html, 500


@app.route("/", defaults={"path": ""})
@app.route("/<path:path>", methods=["GET"])
def get_article(path):
    full_url = request.url
    parts = full_url.split("/", 4)
    if len(parts) >= 5:
        actual_url = "https://" + parts[4].lstrip("/")
        try:
            return bypass_paywall(actual_url)
        except requests.exceptions.RequestException as e:
            return str(e), 400
        except e:
            raise e
    else:
        return "Invalid URL", 400


app.run(host="0.0.0.0", port=5000, debug=False)
