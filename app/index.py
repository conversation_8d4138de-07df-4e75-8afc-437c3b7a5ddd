import flask
import requests
from flask import request
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
import json
import re
import time

app = flask.Flask(__name__)
googlebot_headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.119 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)"
}

# FlareSolverr configuration
FLARESOLVERR_URL = "http://localhost:8191/v1"

def is_cloudflare_blocked(html_content):
    """Check if the response indicates Cloudflare protection"""
    cloudflare_indicators = [
        "cloudflare",
        "cf-ray",
        "checking your browser",
        "ddos protection",
        "security check",
        "ray id",
        "performance & security by cloudflare"
    ]

    content_lower = html_content.lower()
    return any(indicator in content_lower for indicator in cloudflare_indicators)

def use_flaresolverr(url):
    """Use FlareSolverr to bypass Cloudflare protection"""
    print(f"🔧 Using FlareSolverr for: {url}", flush=True)

    payload = {
        "cmd": "request.get",
        "url": url,
        "maxTimeout": 60000,
        "userAgent": googlebot_headers["User-Agent"]
    }

    try:
        response = requests.post(FLARESOLVERR_URL, json=payload, timeout=70)
        response.raise_for_status()

        result = response.json()
        if result.get("status") == "ok":
            solution = result.get("solution", {})
            return solution.get("response", "")
        else:
            print(f"❌ FlareSolverr failed: {result.get('message', 'Unknown error')}", flush=True)
            return None

    except Exception as e:
        print(f"❌ FlareSolverr error: {e}", flush=True)
        return None

def extract_medium_content(html_content):
    """Extract article content from Medium's JSON data"""
    try:
        # Look for the Apollo state data
        apollo_match = re.search(r'window\.__APOLLO_STATE__\s*=\s*({.*?});', html_content, re.DOTALL)
        if not apollo_match:
            return None

        apollo_data = json.loads(apollo_match.group(1))

        # Find the post data
        post_data = None
        for key, value in apollo_data.items():
            if isinstance(value, dict) and value.get("__typename") == "Post":
                content = value.get("content({\"postMeteringOptions\":{\"referrer\":\"\"}})") or value.get("content")
                if content and content.get("bodyModel"):
                    post_data = value
                    break

        if not post_data:
            return None

        # Extract title
        title = post_data.get("title", "")

        # Extract author
        creator_ref = post_data.get("creator", {}).get("__ref", "")
        author_name = ""
        if creator_ref:
            author_data = apollo_data.get(creator_ref, {})
            author_name = author_data.get("name", "")

        # Extract paragraphs
        content_data = post_data.get("content({\"postMeteringOptions\":{\"referrer\":\"\"}})") or post_data.get("content", {})
        body_model = content_data.get("bodyModel", {})
        paragraphs = body_model.get("paragraphs", [])

        # Build HTML content
        html_parts = [
            "<!DOCTYPE html>",
            "<html>",
            "<head>",
            f"<title>{title}</title>",
            "<meta charset='utf-8'>",
            "<style>",
            "body { font-family: Georgia, serif; max-width: 700px; margin: 0 auto; padding: 20px; line-height: 1.6; }",
            "h1 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }",
            "h2, h3 { color: #444; margin-top: 30px; }",
            ".author { color: #666; font-style: italic; margin-bottom: 30px; }",
            "p { margin-bottom: 16px; }",
            "img { max-width: 100%; height: auto; margin: 20px 0; }",
            "a { color: #1a73e8; text-decoration: none; }",
            "a:hover { text-decoration: underline; }",
            "</style>",
            "</head>",
            "<body>",
            f"<h1>{title}</h1>",
            f"<div class='author'>By {author_name}</div>"
        ]

        # Process paragraphs
        for para_ref in paragraphs:
            if isinstance(para_ref, dict) and para_ref.get("__ref"):
                para_data = apollo_data.get(para_ref["__ref"], {})
            else:
                para_data = para_ref

            if not isinstance(para_data, dict):
                continue

            para_type = para_data.get("type", "")
            text = para_data.get("text", "")

            if para_type == "H3":
                html_parts.append(f"<h2>{text}</h2>")
            elif para_type == "H4":
                html_parts.append(f"<h3>{text}</h3>")
            elif para_type == "P" and text:
                # Process markups (links, bold, etc.)
                markups = para_data.get("markups", [])
                processed_text = text

                # Apply markups in reverse order to maintain positions
                for markup in reversed(markups):
                    start = markup.get("start", 0)
                    end = markup.get("end", 0)
                    markup_type = markup.get("type", "")

                    if markup_type == "A":
                        href = markup.get("href", "")
                        processed_text = (processed_text[:start] +
                                        f'<a href="{href}">' +
                                        processed_text[start:end] +
                                        '</a>' +
                                        processed_text[end:])
                    elif markup_type == "STRONG":
                        processed_text = (processed_text[:start] +
                                        '<strong>' +
                                        processed_text[start:end] +
                                        '</strong>' +
                                        processed_text[end:])

                html_parts.append(f"<p>{processed_text}</p>")
            elif para_type == "IMG":
                # Handle images
                metadata = para_data.get("metadata", {})
                if isinstance(metadata, dict) and metadata.get("__ref"):
                    img_data = apollo_data.get(metadata["__ref"], {})
                    img_id = img_data.get("id", "")
                    if img_id:
                        img_url = f"https://miro.medium.com/max/1200/{img_id}"
                        html_parts.append(f'<img src="{img_url}" alt="Article image" />')

        html_parts.extend(["</body>", "</html>"])

        return "\n".join(html_parts)

    except Exception as e:
        print(f"❌ Error extracting Medium content: {e}", flush=True)
        return None

def add_base_tag(html_content, original_url):
    soup = BeautifulSoup(html_content, 'html.parser')
    parsed_url = urlparse(original_url)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}/"
    
    # Handle paths that are not root, e.g., "https://x.com/some/path/w.html"
    if parsed_url.path and not parsed_url.path.endswith('/'):
        base_url = urljoin(base_url, parsed_url.path.rsplit('/', 1)[0] + '/')
    base_tag = soup.find('base')
    
    print(base_url)
    if not base_tag:
        new_base_tag = soup.new_tag('base', href=base_url)
        if soup.head:
            soup.head.insert(0, new_base_tag)
        else:
            head_tag = soup.new_tag('head')
            head_tag.insert(0, new_base_tag)
            soup.insert(0, head_tag)
    
    return str(soup)

def bypass_paywall(url):
    """
    Bypass paywall for a given url with FlareSolverr fallback
    """
    print(f"🚀 Starting bypass_paywall for: {url}", flush=True)

    if not url.startswith("http"):
        try:
            return bypass_paywall("https://" + url)
        except requests.exceptions.RequestException:
            return bypass_paywall("http://" + url)

    print(f"🌐 Fetching: {url}", flush=True)

    try:
        # First, try with regular GoogleBot headers
        print("📱 Trying GoogleBot method...", flush=True)
        response = requests.get(url, headers=googlebot_headers, timeout=30)
        response.encoding = response.apparent_encoding
        html_content = response.text

        print(f"📊 Response status: {response.status_code}", flush=True)
        print(f"📏 Content length: {len(html_content)}", flush=True)

        # Check if we got blocked by Cloudflare
        if response.status_code == 403 or is_cloudflare_blocked(html_content):
            print("🔒 Cloudflare protection detected, using FlareSolverr...", flush=True)

            # Use FlareSolverr to bypass Cloudflare
            flare_content = use_flaresolverr(url)
            if flare_content:
                print("✅ FlareSolverr successful!", flush=True)
                html_content = flare_content
            else:
                print("❌ FlareSolverr failed, using original content", flush=True)
        else:
            print("✅ No Cloudflare protection detected", flush=True)

        # Check if this is a Medium article and extract content from JSON
        print(f"🔍 Checking Medium conditions for {url}:", flush=True)
        print(f"   - Is medium.com: {'medium.com' in url}", flush=True)
        print(f"   - Has member-only story: {'member-only story' in html_content.lower()}", flush=True)
        print(f"   - Has something went wrong: {'something went wrong' in html_content.lower()}", flush=True)
        print(f"   - Has 500: {'500' in html_content}", flush=True)
        print(f"   - Has isLockedPreviewOnly: {'isLockedPreviewOnly\":true' in html_content}", flush=True)

        if "medium.com" in url and ("member-only story" in html_content.lower() or "something went wrong" in html_content.lower() or "500" in html_content or "isLockedPreviewOnly\":true" in html_content):
            print("📰 Detected Medium article with paywall/error, extracting from JSON...", flush=True)
            medium_content = extract_medium_content(html_content)
            if medium_content:
                print("✅ Successfully extracted Medium content from JSON!", flush=True)
                return medium_content
            else:
                print("❌ Failed to extract Medium content from JSON", flush=True)

        # Return processed HTML with base tag
        return add_base_tag(html_content, response.url if 'response' in locals() else url)

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}", flush=True)
        return f"<html><body><h1>Error</h1><p>Failed to fetch the article: {e}</p></body></html>", 400
    except Exception as e:
        print(f"❌ Unexpected error: {e}", flush=True)
        return f"<html><body><h1>Error</h1><p>An unexpected error occurred: {e}</p></body></html>", 500


@app.route("/")
def main_page():
    return flask.send_from_directory(".", "index.html")


@app.route("/article", methods=["POST"])
def show_article():
    link = flask.request.form["link"]
    try:
        return bypass_paywall(link)
    except requests.exceptions.RequestException as e:
        return str(e), 400
    except Exception as exc:
        raise exc


@app.route("/", defaults={"path": ""})
@app.route("/<path:path>", methods=["GET"])
def get_article(path):
    full_url = request.url
    parts = full_url.split("/", 4)
    if len(parts) >= 5:
        actual_url = "https://" + parts[4].lstrip("/")
        try:
            return bypass_paywall(actual_url)
        except requests.exceptions.RequestException as e:
            return str(e), 400
        except e:
            raise e
    else:
        return "Invalid URL", 400


app.run(debug=False)
