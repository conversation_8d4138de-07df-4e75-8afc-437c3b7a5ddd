#!/usr/bin/env python3

import asyncio
from playwright.async_api import async_playwright
import time

async def test_web_interface():
    """Test the web interface directly with <PERSON><PERSON>"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Show browser
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🌐 Opening 13ft web interface...")
            await page.goto("http://localhost:5001", wait_until="networkidle")
            
            print("📝 Filling in the Medium URL...")
            url = "https://medium.com/devdotcom/top-9-open-source-kubernetes-projects-every-devops-engineer-needs-in-2025-98a969290ce6"
            
            # Find the input field and fill it
            await page.fill('input[name="link"]', url)
            
            print("🚀 Submitting the form...")
            # Click submit button
            await page.click('input[type="submit"]')
            
            print("⏳ Waiting 25 seconds for processing...")
            await page.wait_for_timeout(25000)  # Wait 25 seconds as requested
            
            # Check what we got
            title = await page.title()
            print(f"📄 Page title: {title}")
            
            # Check for error indicators
            content = await page.content()
            
            if "500" in content and "something went wrong" in content.lower():
                print("❌ Found 500 error on page")
                
                # Save the error page
                with open("error_page.html", 'w', encoding='utf-8') as f:
                    f.write(content)
                print("💾 Error page saved to error_page.html")
                
            elif "Top 9 Open-Source Kubernetes Projects" in content:
                print("✅ Article content found!")
                print("🎉 Success! The paywall bypass is working!")
                
                # Save the success page
                with open("success_page.html", 'w', encoding='utf-8') as f:
                    f.write(content)
                print("💾 Success page saved to success_page.html")
                
            else:
                print("⚠️  Unexpected content")
                print(f"Content length: {len(content)}")
                
                # Save for inspection
                with open("unexpected_page.html", 'w', encoding='utf-8') as f:
                    f.write(content)
                print("💾 Page saved to unexpected_page.html")
            
            # Take a screenshot
            await page.screenshot(path="web_interface_test.png")
            print("📸 Screenshot saved to web_interface_test.png")
            
            print("\n🔍 Press Enter to close browser...")
            input()
            
        except Exception as e:
            print(f"❌ Error: {e}")
            await page.screenshot(path="error_screenshot.png")
            print("📸 Error screenshot saved")
        
        await browser.close()

if __name__ == "__main__":
    print("🧪 Testing 13ft web interface with Playwright...")
    asyncio.run(test_web_interface())
