services:
  13ft:
    container_name: 13ft
    hostname: 13ft
    build: .
    restart: unless-stopped
    ports:
      - "5001:5000"
    environment:
      - FLARESOLVERR_HOST=http://flaresolverr:8191
    depends_on:
      - flaresolverr

  flaresolverr:
    container_name: flaresolverr
    hostname: flaresolverr
    image: ghcr.io/flaresolverr/flaresolverr:latest
    restart: unless-stopped
    ports:
      - "8191:8191"
    environment:
      - LOG_LEVEL=info
      - LOG_HTML=false
      - CAPTCHA_SOLVER=none
      - TZ=UTC
