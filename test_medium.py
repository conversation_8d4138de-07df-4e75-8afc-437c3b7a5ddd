#!/usr/bin/env python3

import asyncio
from playwright.async_api import async_playwright
import requests

async def test_medium_with_playwright():
    """Test Medium URL with <PERSON><PERSON> to see what's happening"""
    
    url = "https://medium.com/devdotcom/top-9-open-source-kubernetes-projects-every-devops-engineer-needs-in-2025-98a969290ce6"
    
    async with async_playwright() as p:
        # Test with different browsers
        for browser_type in [p.chromium, p.firefox]:
            browser_name = browser_type.name
            print(f"\n=== Testing with {browser_name} ===")
            
            browser = await browser_type.launch(headless=True)
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.119 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)"
            )
            
            page = await context.new_page()
            
            try:
                # Navigate to the page
                response = await page.goto(url, wait_until="networkidle", timeout=30000)
                print(f"Status: {response.status}")
                print(f"URL: {response.url}")
                
                # Wait a bit for any dynamic content
                await page.wait_for_timeout(3000)
                
                # Get the page content
                content = await page.content()
                print(f"Content length: {len(content)}")
                
                # Check for specific indicators
                if "500" in content and "something went wrong" in content.lower():
                    print("❌ Found 500 error in content")
                elif "cloudflare" in content.lower():
                    print("🔒 Cloudflare protection detected")
                elif "paywall" in content.lower() or "member-only" in content.lower():
                    print("💰 Paywall detected")
                elif len(content) > 50000:  # Reasonable size for full article
                    print("✅ Looks like full content retrieved")
                else:
                    print("⚠️  Partial or blocked content")
                
                # Check title
                title = await page.title()
                print(f"Title: {title}")
                
                # Save content for inspection
                filename = f"medium_content_{browser_name}.html"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"Content saved to {filename}")
                
            except Exception as e:
                print(f"Error: {e}")
            
            await browser.close()

def test_with_requests():
    """Test with requests library like our current implementation"""
    print("\n=== Testing with requests (current method) ===")
    
    url = "https://medium.com/devdotcom/top-9-open-source-kubernetes-projects-every-devops-engineer-needs-in-2025-98a969290ce6"
    headers = {
        "User-Agent": "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.119 Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)"
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Content length: {len(response.text)}")
        
        if "500" in response.text and "something went wrong" in response.text.lower():
            print("❌ Found 500 error in content")
        elif "cloudflare" in response.text.lower():
            print("🔒 Cloudflare protection detected")
        elif len(response.text) > 50000:
            print("✅ Looks like full content retrieved")
        else:
            print("⚠️  Partial or blocked content")
            
        # Save content for inspection
        with open("medium_content_requests.html", 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("Content saved to medium_content_requests.html")
        
    except Exception as e:
        print(f"Error: {e}")

def test_13ft_service():
    """Test our 13ft service directly"""
    print("\n=== Testing 13ft service ===")
    
    url = "https://medium.com/devdotcom/top-9-open-source-kubernetes-projects-every-devops-engineer-needs-in-2025-98a969290ce6"
    
    try:
        response = requests.post(
            "http://localhost:5001/article",
            data={"link": url},
            timeout=60
        )
        print(f"Status: {response.status_code}")
        print(f"Content length: {len(response.text)}")
        
        if "500" in response.text and "something went wrong" in response.text.lower():
            print("❌ Found 500 error in content")
        elif "cloudflare" in response.text.lower():
            print("🔒 Cloudflare protection detected")
        elif len(response.text) > 50000:
            print("✅ Looks like full content retrieved")
        else:
            print("⚠️  Partial or blocked content")
            
        # Save content for inspection
        with open("medium_content_13ft.html", 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("Content saved to medium_content_13ft.html")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    print("🔍 Investigating Medium URL behavior...")
    
    # Test with requests first
    test_with_requests()
    
    # Test our 13ft service
    test_13ft_service()
    
    # Test with Playwright
    asyncio.run(test_medium_with_playwright())
    
    print("\n✅ Investigation complete! Check the saved HTML files for details.")
